<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>拓扑图测试页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: #0a0e1a;
            color: #ffffff;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }
        .btn {
            background: linear-gradient(135deg, #00d4ff, #0099cc);
            border: none;
            color: white;
            padding: 15px 30px;
            margin: 10px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 212, 255, 0.4);
        }
        .image-preview {
            margin: 20px 0;
            border: 2px solid #00d4ff;
            border-radius: 10px;
            overflow: hidden;
            max-width: 100%;
        }
        .image-preview img {
            width: 100%;
            height: auto;
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>拓扑图测试页面</h1>
        <p>点击下面的按钮测试拓扑图链接</p>
        
        <button class="btn" onclick="openSystemTopology()">打开SVG系统拓扑图</button>
        <button class="btn" onclick="openCoolingTopology()">打开水冷系统拓扑图</button>
        
        <div class="image-preview">
            <h3>SVG系统拓扑图预览</h3>
            <img src="../docs/SVG系统拓扑图.png" alt="SVG系统拓扑图" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
            <p style="display:none; color:#ff4444;">图片加载失败</p>
        </div>
        
        <div class="image-preview">
            <h3>水冷系统拓扑图预览</h3>
            <img src="../docs/水冷系统拓扑图.png" alt="水冷系统拓扑图" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
            <p style="display:none; color:#ff4444;">图片加载失败</p>
        </div>
    </div>

    <script>
        /**
         * 打开SVG系统拓扑图
         */
        function openSystemTopology() {
            console.log('打开SVG系统拓扑图');
            window.open('../docs/SVG系统拓扑图.png', '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');
        }

        /**
         * 打开水冷系统拓扑图
         */
        function openCoolingTopology() {
            console.log('打开水冷系统拓扑图');
            window.open('../docs/水冷系统拓扑图.png', '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');
        }
    </script>
</body>
</html>
